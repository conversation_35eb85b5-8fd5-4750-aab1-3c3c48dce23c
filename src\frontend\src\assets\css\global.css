/* 全局样式 */
:root {
  /* 主题颜色 */
  --primary-color: #4299e1;
  --secondary-color: #1a365d;
  --background-color: #f0f5ff;
  --text-color: #2d3748;
  --light-text-color: #718096;
  --border-color: #e2e8f0;
  
  /* 玻璃态效果 */
  --glass-background: rgba(255, 255, 255, 0.7);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  --glass-blur: blur(10px);
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
}

#app {
  height: 100%;
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

/* 玻璃态卡片 */
.glass-card {
  background: var(--glass-background);
  border-radius: 15px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  padding: 20px;
}

/* 主按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
}

/* 表单样式覆盖 */
.el-form-item__label {
  color: var(--text-color);
}

.el-input__inner {
  border-radius: 8px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .glass-card {
    padding: 15px;
  }
}