package com.lightweightsys.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * JWT响应DTO
 */
@Data
@AllArgsConstructor
public class JwtResponse {
    
    private String token;
    private String type = "Bearer";
    private Long id;
    private String username;
    private String name;
    private String email;
    private List<String> roles;
    
    public JwtResponse(String token, Long id, String username, String name, String email, List<String> roles) {
        this.token = token;
        this.id = id;
        this.username = username;
        this.name = name;
        this.email = email;
        this.roles = roles;
    }
}