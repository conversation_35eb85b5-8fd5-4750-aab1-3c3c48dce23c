# 前提
请通读1_prompts\rules下的java-development.md作为java开发规范。
请通读1_prompts\rules下的vue-development.md作为vue开发规范。

# 系统命名
lightweightsys ：轻量级管理系统

# 1. 技术栈选择
## 后端（Java）
Spring Boot: 用于快速构建独立的、生产级别的Spring应用。

Spring Data JPA/MyBatis: 用于数据库操作，JPA提供更高级的抽象，MyBatis提供更直接的SQL控制。

Spring Security: 用于处理认证和授权。

## 前端（Vue）
Vue.js: 构建用户界面的框架。

Vue Router: 用于构建单页面应用（SPA）。

Vuex: 状态管理库。

Axios: 用于在后端和前端之间进行HTTP通信。

Element UI/Vuetify/Ant Design Vue: UI组件库，用于快速开发界面。

# 2. 系统设计原则
前后端分离
前后端分离：确保前后端可以独立开发和部署，提高开发效率和维护性。

RESTful API设计：后端提供RESTful API，前端通过这些API与后端交互。

安全性
数据加密：确保数据传输过程中的安全性，使用HTTPS。

输入验证：在服务器端对所有输入数据进行验证，防止SQL注入、XSS攻击等。

权限控制：使用Spring Security等工具进行用户认证和权限管理。

# 3. 开发规则
后端开发规则

数据库初始化脚本：编写数据库初始化脚本，用于创建数据库表和初始数据。

数据库初始化脚本存放位置：sql\exam.sql，同步放一份到java工程中。

数据库初始化脚本执行：在项目启动时自动执行数据库初始化脚本。

前端开发规则
组件化开发：使用Vue组件化开发，提高代码复用性。

代码风格一致性：使用ESLint等工具保持代码风格一致性。

响应式布局：使用Flexbox或Grid系统实现响应式设计。

# 4. 实施步骤
项目初始化
创建Spring Boot项目：使用Spring Initializr（https://start.spring.io/）创建项目。

Spring Boot项目存放于src/backend目录下。

创建Vue项目：使用Vue CLI（https://cli.vuejs.org/）创建项目。

Vue项目存放于src/frontend目录下。

后端实现：参考java开发规范

前端实现：参考vue开发规范

UI组件：使用Element UI/Vuetify/Ant Design Vue等库快速开发界面。

界面风格：采用Glassmorphism Tech Blue UI设计风格，主色调为浅蓝色，使用半透明玻璃态效果。

界面需求：
    1、登录页面包含顶部Logo区域、中央登录卡片和底部版权信息。登录成功页面显示欢迎信息和用户信息。全面支持响应式设计，适配桌面、平板和移动设备。
    2、首页包含顶部导航栏、左侧菜单栏和右侧内容区域。支持多级菜单和动态路由。支持响应式设计，适配桌面、平板和移动设备。
    3、系统管理页面包含顶部导航栏、左侧菜单栏和右侧内容区域。支持多级菜单和动态路由。支持响应式设计，适配桌面、平板和移动设备。
# 5. 测试与部署
单元测试与集成测试：为后端和前端编写测试用例。

# 6. 提醒
我的操作环境是windows11，所以有些命令可能不适用于macOS或Linux系统。

代码生成后更新一下README.md，说明一下整个系统的启动方式。

如涉及账号密码相关初始化生成(如数据库连接信息、redis连接信息、管理员账号初始化信息)，请按照数字+字母+特殊字符的格式生成(数字部分严禁出现123456、111111、666666、888888类似的弱组合)，避免出现弱口令的情况。

# 7. 总结
本文介绍了使用Java+Vue构建一个轻量级管理系统，包括技术栈选择、系统设计原则、开发规则和实施步骤。通过遵循这些规则和步骤，可以构建一个稳定、安全且易于维护的系统。