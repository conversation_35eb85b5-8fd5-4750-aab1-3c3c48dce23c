<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>后端API测试页面</h1>
    
    <div class="test-section">
        <h3>1. 测试后端连接</h3>
        <button onclick="testConnection()">测试连接</button>
        <div id="connection-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试登录API</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试菜单API（需要先登录）</h3>
        <button onclick="testMenus()">测试菜单</button>
        <div id="menu-result" class="result"></div>
    </div>

    <script>
        let authToken = '';
        
        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.textContent = '正在测试连接...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                resultDiv.textContent = `连接测试结果：
状态码: ${response.status}
状态文本: ${response.statusText}
CORS头: ${response.headers.get('Access-Control-Allow-Origin') || '未设置'}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `连接失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.textContent = '正在测试登录...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin@9527#'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    resultDiv.textContent = `登录成功！
状态码: ${response.status}
Token: ${data.token ? data.token.substring(0, 50) + '...' : '无'}
用户信息: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `登录失败：
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `登录请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testMenus() {
            const resultDiv = document.getElementById('menu-result');
            
            if (!authToken) {
                resultDiv.textContent = '请先登录获取Token';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在获取菜单...';
            
            try {
                const response = await fetch('http://localhost:8080/api/menus/user', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `菜单获取成功！
状态码: ${response.status}
菜单数据: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `菜单获取失败：
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `菜单请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
