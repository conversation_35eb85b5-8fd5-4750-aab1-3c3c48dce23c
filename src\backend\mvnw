#!/bin/sh
# ----------------------------------------------------------------------------
# Maven启动脚本，用于Unix/Linux环境
# ----------------------------------------------------------------------------

# 查找包含项目基本目录的.mvn目录
# 如果找到，则假定它是Maven包装器的基本目录
MAVEN_PROJECTBASEDIR=${MAVEN_BASEDIR:-$(pwd)}
if [ -f "$MAVEN_PROJECTBASEDIR/.mvn/wrapper/maven-wrapper.properties" ]; then
  MAVEN_JAVA_EXE="$JAVA_HOME/bin/java"
  WRAPPER_JAR="$MAVEN_PROJECTBASEDIR/.mvn/wrapper/maven-wrapper.jar"
  WRAPPER_LAUNCHER=org.apache.maven.wrapper.MavenWrapperMain

  exec "$MAVEN_JAVA_EXE" \
    $MAVEN_OPTS \
    -classpath "$WRAPPER_JAR" \
    "-Dmaven.multiModuleProjectDirectory=$MAVEN_PROJECTBASEDIR" \
    $WRAPPER_LAUNCHER $MAVEN_CONFIG "$@"
else
  echo "找不到 $MAVEN_PROJECTBASEDIR/.mvn/wrapper/maven-wrapper.properties 文件，请检查您的Maven安装。"
  exit 1
fi