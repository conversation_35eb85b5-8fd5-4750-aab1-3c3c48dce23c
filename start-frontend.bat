@echo off
echo Starting frontend server...
cd src\frontend
echo Current directory: %CD%
echo.
echo Checking if node_modules exists...
if exist node_modules (
    echo Dependencies found, starting development server...
    npm run serve
) else (
    echo Dependencies not found, installing...
    npm install
    if %ERRORLEVEL% EQU 0 (
        echo Installation successful, starting development server...
        npm run serve
    ) else (
        echo Installation failed
        pause
    )
)
pause
