@REM ----------------------------------------------------------------------------
@REM Maven启动脚本，用于Windows环境
@REM ----------------------------------------------------------------------------

@REM 开始所有变量的本地作用域
@setlocal

@REM 设置Maven项目的基本目录
@set MAVEN_PROJECTBASEDIR=%MAVEN_BASEDIR%
if "%MAVEN_PROJECTBASEDIR%"=="" set MAVEN_PROJECTBASEDIR=%~dp0

@REM 查找包含项目基本目录的.mvn目录
@REM 如果找到，则假定它是Maven包装器的基本目录
if exist "%MAVEN_PROJECTBASEDIR%\.mvn\wrapper\maven-wrapper.properties" goto baseDirFound
echo 找不到 %MAVEN_PROJECTBASEDIR%\.mvn\wrapper\maven-wrapper.properties 文件，请检查您的Maven安装。
goto error

:baseDirFound
set MAVEN_JAVA_EXE="%JAVA_HOME%\bin\java.exe"
set WRAPPER_JAR="%MAVEN_PROJECTBASEDIR%\.mvn\wrapper\maven-wrapper.jar"
set WRAPPER_LAUNCHER=org.apache.maven.wrapper.MavenWrapperMain

%MAVEN_JAVA_EXE% ^
  %MAVEN_OPTS% ^
  -classpath %WRAPPER_JAR% ^
  "-Dmaven.multiModuleProjectDirectory=%MAVEN_PROJECTBASEDIR%" ^
  %WRAPPER_LAUNCHER% %MAVEN_CONFIG% %*
if ERRORLEVEL 1 goto error
goto end

:error
echo Maven启动脚本出错
exit /b 1

:end
@endlocal