<template>
  <div class="menu-manage-container">
    <el-card class="glass-card">
      <div slot="header" class="clearfix">
        <span>菜单管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          icon="el-icon-plus"
          @click="handleAdd">
          添加菜单
        </el-button>
      </div>
      
      <!-- 菜单列表 -->
      <el-table
        :data="menuList"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <el-table-column prop="name" label="菜单名称" width="180"></el-table-column>
        <el-table-column prop="path" label="路径" width="180"></el-table-column>
        <el-table-column prop="component" label="组件路径"></el-table-column>
        <el-table-column label="图标" width="100">
          <template slot-scope="scope">
            <i :class="scope.row.icon"></i>
            <span style="margin-left: 5px">{{ scope.row.icon }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80"></el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button 
              type="primary" 
              icon="el-icon-edit" 
              size="mini" 
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              size="mini" 
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑菜单对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="40%" 
      @close="resetForm">
      <el-form 
        :model="menuForm" 
        :rules="menuFormRules" 
        ref="menuFormRef" 
        label-width="100px">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="menuForm.name"></el-input>
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <el-input v-model="menuForm.path"></el-input>
        </el-form-item>
        <el-form-item label="组件路径" prop="component">
          <el-input v-model="menuForm.component"></el-input>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="menuForm.icon">
            <template slot="prepend">
              <i :class="menuForm.icon"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="menuForm.sortOrder" :min="0" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="父级菜单" prop="parentId">
          <el-select v-model="menuForm.parentId" placeholder="请选择父级菜单" clearable>
            <el-option label="无 (顶级菜单)" :value="null"></el-option>
            <el-option
              v-for="item in parentMenuOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import menuApi from '@/api/menu'

export default {
  name: 'MenuManage',
  data() {
    return {
      // 菜单列表
      menuList: [],
      // 加载状态
      loading: false,
      // 对话框可见性
      dialogVisible: false,
      // 对话框标题
      dialogTitle: '添加菜单',
      // 编辑模式
      editMode: false,
      // 菜单表单
      menuForm: {
        id: null,
        name: '',
        path: '',
        component: '',
        icon: 'el-icon-menu',
        sortOrder: 0,
        parentId: null
      },
      // 表单验证规则
      menuFormRules: {
        name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' },
          { max: 50, message: '菜单名称长度不能超过50个字符', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '请输入路径', trigger: 'blur' },
          { max: 100, message: '路径长度不能超过100个字符', trigger: 'blur' }
        ],
        component: [
          { max: 100, message: '组件路径长度不能超过100个字符', trigger: 'blur' }
        ],
        icon: [
          { max: 50, message: '图标长度不能超过50个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 父级菜单选项
    parentMenuOptions() {
      // 过滤掉当前编辑的菜单及其子菜单
      const filterMenus = (menus, id) => {
        return menus.filter(menu => {
          if (menu.id === id) return false
          if (menu.children && menu.children.length > 0) {
            menu.children = filterMenus(menu.children, id)
          }
          return true
        })
      }
      
      // 扁平化菜单列表
      const flattenMenus = (menus, result = []) => {
        menus.forEach(menu => {
          result.push({
            id: menu.id,
            name: menu.name
          })
          if (menu.children && menu.children.length > 0) {
            flattenMenus(menu.children, result)
          }
        })
        return result
      }
      
      let filteredMenus = this.menuList
      if (this.editMode && this.menuForm.id) {
        filteredMenus = filterMenus([...this.menuList], this.menuForm.id)
      }
      
      return flattenMenus(filteredMenus)
    }
  },
  created() {
    this.getMenuList()
  },
  methods: {
    // 获取菜单列表
    async getMenuList() {
      this.loading = true
      try {
        const res = await menuApi.getAllMenus()
        this.menuList = res.data.data
      } catch (error) {
        this.$message.error('获取菜单列表失败')
      } finally {
        this.loading = false
      }
    },
    // 处理添加菜单
    handleAdd() {
      this.dialogTitle = '添加菜单'
      this.editMode = false
      this.dialogVisible = true
    },
    // 处理编辑菜单
    handleEdit(row) {
      this.dialogTitle = '编辑菜单'
      this.editMode = true
      this.menuForm = {
        id: row.id,
        name: row.name,
        path: row.path,
        component: row.component,
        icon: row.icon,
        sortOrder: row.sortOrder,
        parentId: row.parent ? row.parent.id : null
      }
      this.dialogVisible = true
    },
    // 处理删除菜单
    handleDelete(row) {
      // 如果有子菜单，不允许删除
      if (row.children && row.children.length > 0) {
        return this.$message.warning('该菜单下有子菜单，不能删除')
      }
      
      this.$confirm('确认删除该菜单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await menuApi.deleteMenu(row.id)
          this.$message.success('删除成功')
          this.getMenuList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },
    // 提交表单
    submitForm() {
      this.$refs.menuFormRef.validate(async valid => {
        if (!valid) return
        
        try {
          if (this.editMode) {
            // 编辑菜单
            await menuApi.updateMenu(this.menuForm.id, this.menuForm)
            this.$message.success('更新菜单成功')
          } else {
            // 添加菜单
            await menuApi.createMenu(this.menuForm)
            this.$message.success('添加菜单成功')
          }
          this.dialogVisible = false
          this.getMenuList()
        } catch (error) {
          this.$message.error(error.response?.data?.message || '操作失败')
        }
      })
    },
    // 重置表单
    resetForm() {
      this.$refs.menuFormRef?.resetFields()
      this.menuForm = {
        id: null,
        name: '',
        path: '',
        component: '',
        icon: 'el-icon-menu',
        sortOrder: 0,
        parentId: null
      }
    }
  }
}
</script>

<style scoped>
.menu-manage-container {
  padding: 20px;
}
</style>