import axios from 'axios'

/**
 * 用户相关的API接口封装
 */
export default {
  /**
   * 获取所有用户
   * @returns {Promise} - 返回Promise对象
   */
  getAllUsers() {
    return axios.get('/users')
  },

  /**
   * 根据ID获取用户
   * @param {Number} id - 用户ID
   * @returns {Promise} - 返回Promise对象
   */
  getUserById(id) {
    return axios.get(`/users/${id}`)
  },

  /**
   * 删除用户
   * @param {Number} id - 用户ID
   * @returns {Promise} - 返回Promise对象
   */
  deleteUser(id) {
    return axios.delete(`/users/${id}`)
  }
}