<template>
  <el-container class="home-container">
    <!-- 头部区域 -->
    <el-header>
      <div class="header-left">
        <div class="collapse-btn" @click="toggleCollapse">
          <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </div>
        <h1 class="system-title">轻量级管理系统</h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <i class="el-icon-user"></i>
            {{ userInfo.name || userInfo.username }}
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="profile">个人信息</el-dropdown-item>
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>
    
    <!-- 主体区域 -->
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'">
        <el-menu
          :default-active="activePath"
          :collapse="isCollapse"
          :collapse-transition="false"
          :unique-opened="true"
          background-color="#1a365d"
          text-color="#fff"
          active-text-color="#4299e1"
          router>
          <!-- 遍历生成菜单项 -->
          <!-- 遍历生成菜单项 -->
          <template v-for="menu in menus">
            <!-- 包含子菜单的菜单项 -->
            <el-submenu v-if="menu.children && menu.children.length > 0" :index="menu.path" :key="'sub-'+menu.id">
              <template slot="title">
                <i :class="menu.icon"></i>
                <span>{{ menu.name }}</span>
              </template>
              <!-- 子菜单项 -->
              <el-menu-item 
                v-for="subMenu in menu.children" 
                :key="subMenu.id" 
                :index="subMenu.path"
                @click="saveNavState(subMenu.path)">
                <i :class="subMenu.icon"></i>
                <span>{{ subMenu.name }}</span>
              </el-menu-item>
            </el-submenu>
            <!-- 不包含子菜单的菜单项 -->
            <el-menu-item 
              v-else 
              :index="menu.path" 
              :key="'item-'+menu.id"
              @click="saveNavState(menu.path)">
              <i :class="menu.icon"></i>
              <span>{{ menu.name }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>
      
      <!-- 内容区域 -->
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Home',
  data() {
    return {
      isCollapse: false,
      activePath: ''
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'menus'])
  },
  created() {
    // 获取菜单数据
    this.getMenus()
    // 获取保存的导航状态
    this.activePath = localStorage.getItem('activePath') || '/welcome'
  },
  methods: {
    // 切换菜单折叠状态
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    },
    // 处理下拉菜单命令
    handleCommand(command) {
      if (command === 'logout') {
        this.logout()
      } else if (command === 'profile') {
        this.$message.info('功能开发中')
      }
    },
    // 退出登录
    logout() {
      this.$confirm('确认退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('logout')
        this.$router.push('/login')
        this.$message.success('退出登录成功')
      }).catch(() => {})
    },
    // 保存导航状态
    saveNavState(path) {
      localStorage.setItem('activePath', path)
      this.activePath = path
    },
    // 获取菜单数据
    async getMenus() {
      try {
        await this.$store.dispatch('getMenus')
      } catch (error) {
        this.$message.error('获取菜单失败')
      }
    }
  }
}
</script>

<style scoped>
.home-container {
  height: 100%;
}

.el-header {
  background-color: var(--secondary-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  padding: 0 15px;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 15px;
}

.system-title {
  font-size: 20px;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  color: white;
  cursor: pointer;
}

.user-info i {
  margin: 0 5px;
}

.el-aside {
  background-color: var(--secondary-color);
  transition: width 0.3s;
}

.el-menu {
  border-right: none;
}

.el-main {
  background-color: var(--background-color);
  padding: 20px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .system-title {
    font-size: 16px;
  }
  
  .el-header {
    padding: 0 10px;
  }
}
</style>