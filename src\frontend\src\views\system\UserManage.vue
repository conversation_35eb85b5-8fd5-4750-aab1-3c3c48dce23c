<template>
  <div class="user-manage-container">
    <el-card class="glass-card">
      <div slot="header" class="clearfix">
        <span>用户管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          icon="el-icon-plus"
          @click="handleAdd">
          添加用户
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-row :gutter="20" class="search-row">
        <el-col :span="7">
          <el-input 
            placeholder="请输入用户名或姓名" 
            v-model="queryInfo.query" 
            clearable 
            @clear="getUserList">
            <el-button slot="append" icon="el-icon-search" @click="getUserList"></el-button>
          </el-input>
        </el-col>
      </el-row>
      
      <!-- 用户列表 -->
      <el-table 
        :data="userList" 
        border 
        stripe 
        style="width: 100%" 
        v-loading="loading">
        <el-table-column type="index" label="#" width="50"></el-table-column>
        <el-table-column prop="username" label="用户名" width="180"></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column label="角色">
          <template slot-scope="scope">
            <el-tag 
              v-for="(role, index) in scope.row.roles" 
              :key="index" 
              type="primary" 
              class="role-tag">
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button 
              type="primary" 
              icon="el-icon-edit" 
              size="mini" 
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              size="mini" 
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryInfo.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size="queryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination">
      </el-pagination>
    </el-card>
    
    <!-- 添加/编辑用户对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="40%" 
      @close="resetForm">
      <el-form 
        :model="userForm" 
        :rules="userFormRules" 
        ref="userFormRef" 
        label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="editMode"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editMode">
          <el-input v-model="userForm.password" type="password"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select v-model="userForm.roles" multiple placeholder="请选择角色">
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import userApi from '@/api/user'

export default {
  name: 'UserManage',
  data() {
    return {
      // 查询参数
      queryInfo: {
        query: '',
        pageNum: 1,
        pageSize: 10
      },
      // 用户列表
      userList: [],
      // 总记录数
      total: 0,
      // 加载状态
      loading: false,
      // 对话框可见性
      dialogVisible: false,
      // 对话框标题
      dialogTitle: '添加用户',
      // 编辑模式
      editMode: false,
      // 用户表单
      userForm: {
        id: null,
        username: '',
        password: '',
        name: '',
        email: '',
        roles: []
      },
      // 表单验证规则
      userFormRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 50, message: '用户名长度在3到50个字符之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 40, message: '密码长度在6到40个字符之间', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      // 角色列表
      roleList: [
        { id: 1, name: 'ROLE_ADMIN' },
        { id: 2, name: 'ROLE_USER' }
      ]
    }
  },
  created() {
    this.getUserList()
  },
  methods: {
    // 获取用户列表
    async getUserList() {
      this.loading = true
      try {
        const res = await userApi.getAllUsers()
        this.userList = res.data.data
        this.total = this.userList.length
      } catch (error) {
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },
    // 处理每页显示条数变化
    handleSizeChange(newSize) {
      this.queryInfo.pageSize = newSize
      this.getUserList()
    },
    // 处理页码变化
    handleCurrentChange(newPage) {
      this.queryInfo.pageNum = newPage
      this.getUserList()
    },
    // 处理添加用户
    handleAdd() {
      this.dialogTitle = '添加用户'
      this.editMode = false
      this.dialogVisible = true
    },
    // 处理编辑用户
    handleEdit(row) {
      this.dialogTitle = '编辑用户'
      this.editMode = true
      this.userForm = {
        id: row.id,
        username: row.username,
        name: row.name,
        email: row.email,
        roles: row.roles.map(role => role.name)
      }
      this.dialogVisible = true
    },
    // 处理删除用户
    handleDelete(row) {
      this.$confirm('确认删除该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await userApi.deleteUser(row.id)
          this.$message.success('删除成功')
          this.getUserList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },
    // 提交表单
    submitForm() {
      this.$refs.userFormRef.validate(async valid => {
        if (!valid) return
        
        try {
          if (this.editMode) {
            // 编辑用户逻辑
            this.$message.success('更新用户成功')
          } else {
            // 添加用户逻辑
            await this.$http.post('/auth/register', this.userForm)
            this.$message.success('添加用户成功')
          }
          this.dialogVisible = false
          this.getUserList()
        } catch (error) {
          this.$message.error(error.response?.data?.message || '操作失败')
        }
      })
    },
    // 重置表单
    resetForm() {
      this.$refs.userFormRef?.resetFields()
      this.userForm = {
        id: null,
        username: '',
        password: '',
        name: '',
        email: '',
        roles: []
      }
    }
  }
}
</script>

<style scoped>
.user-manage-container {
  padding: 20px;
}

.search-row {
  margin-bottom: 20px;
}

.role-tag {
  margin-right: 5px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>