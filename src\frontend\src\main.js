import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './assets/css/global.css'
import axios from 'axios'

// 配置ElementUI
Vue.use(ElementUI)

// 配置axios
// 开发环境使用代理，生产环境使用完整URL
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? 'http://localhost:8080/api' : '/api'
// 请求拦截器，添加token
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
}, error => {
  return Promise.reject(error)
})

// 响应拦截器，处理错误
axios.interceptors.response.use(response => {
  return response
}, error => {
  if (error.response) {
    if (error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      store.dispatch('logout')
      router.push('/login')
    }
  }
  return Promise.reject(error)
})

Vue.prototype.$http = axios

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')