<template>
  <div class="role-manage-container">
    <el-card class="glass-card">
      <div slot="header" class="clearfix">
        <span>角色管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          icon="el-icon-plus"
          @click="handleAdd">
          添加角色
        </el-button>
      </div>
      
      <!-- 角色列表 -->
      <el-table 
        :data="roleList" 
        border 
        stripe 
        style="width: 100%" 
        v-loading="loading">
        <el-table-column type="index" label="#" width="50"></el-table-column>
        <el-table-column prop="name" label="角色名称" width="180"></el-table-column>
        <el-table-column label="权限菜单">
          <template slot-scope="scope">
            <el-tag 
              v-for="(menu, index) in scope.row.menus" 
              :key="index" 
              type="success" 
              class="menu-tag">
              {{ menu.name }}
            </el-tag>
            <el-button v-if="!scope.row.menus || scope.row.menus.length === 0" type="text" size="small">
              暂无权限
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-button 
              type="primary" 
              icon="el-icon-edit" 
              size="mini" 
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              type="success" 
              icon="el-icon-setting" 
              size="mini" 
              @click="handleAssignRights(scope.row)">
              分配权限
            </el-button>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              size="mini" 
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑角色对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="40%" 
      @close="resetForm">
      <el-form 
        :model="roleForm" 
        :rules="roleFormRules" 
        ref="roleFormRef" 
        label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="roleForm.description" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 分配权限对话框 -->
    <el-dialog 
      title="分配权限" 
      :visible.sync="rightsDialogVisible" 
      width="50%" 
      @close="resetRightsForm">
      <el-tree
        :data="menuList"
        :props="treeProps"
        show-checkbox
        node-key="id"
        default-expand-all
        :default-checked-keys="defaultCheckedKeys"
        ref="menuTree">
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rightsDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRightsForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RoleManage',
  data() {
    return {
      // 角色列表
      roleList: [
        {
          id: 1,
          name: 'ROLE_ADMIN',
          description: '管理员角色',
          menus: [
            { id: 1, name: '首页' },
            { id: 2, name: '系统管理' },
            { id: 3, name: '用户管理' },
            { id: 4, name: '角色管理' },
            { id: 5, name: '菜单管理' }
          ]
        },
        {
          id: 2,
          name: 'ROLE_USER',
          description: '普通用户角色',
          menus: [
            { id: 1, name: '首页' }
          ]
        }
      ],
      // 加载状态
      loading: false,
      // 对话框可见性
      dialogVisible: false,
      // 对话框标题
      dialogTitle: '添加角色',
      // 编辑模式
      editMode: false,
      // 角色表单
      roleForm: {
        id: null,
        name: '',
        description: ''
      },
      // 表单验证规则
      roleFormRules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 3, max: 50, message: '角色名称长度在3到50个字符之间', trigger: 'blur' }
        ]
      },
      // 权限对话框可见性
      rightsDialogVisible: false,
      // 当前编辑的角色ID
      currentRoleId: null,
      // 菜单列表
      menuList: [
        {
          id: 1,
          name: '首页',
          path: '/dashboard',
          children: []
        },
        {
          id: 2,
          name: '系统管理',
          path: '/system',
          children: [
            {
              id: 3,
              name: '用户管理',
              path: '/system/user'
            },
            {
              id: 4,
              name: '角色管理',
              path: '/system/role'
            },
            {
              id: 5,
              name: '菜单管理',
              path: '/system/menu'
            }
          ]
        }
      ],
      // 树形控件属性绑定
      treeProps: {
        label: 'name',
        children: 'children'
      },
      // 默认选中的节点
      defaultCheckedKeys: []
    }
  },
  created() {
    this.getRoleList()
  },
  methods: {
    // 获取角色列表
    async getRoleList() {
      this.loading = true
      try {
        // 模拟API调用
        setTimeout(() => {
          this.loading = false
        }, 500)
      } catch (error) {
        this.$message.error('获取角色列表失败')
        this.loading = false
      }
    },
    // 处理添加角色
    handleAdd() {
      this.dialogTitle = '添加角色'
      this.editMode = false
      this.dialogVisible = true
    },
    // 处理编辑角色
    handleEdit(row) {
      this.dialogTitle = '编辑角色'
      this.editMode = true
      this.roleForm = {
        id: row.id,
        name: row.name,
        description: row.description || ''
      }
      this.dialogVisible = true
    },
    // 处理删除角色
    handleDelete(row) {
      this.$confirm('确认删除该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 使用row参数，避免ESLint警告
        console.log('删除角色:', row.id)
        this.$message.success('删除成功')
        this.getRoleList()
      }).catch(() => {})
    },
    // 处理分配权限
    handleAssignRights(row) {
      this.currentRoleId = row.id
      this.defaultCheckedKeys = row.menus ? row.menus.map(item => item.id) : []
      this.rightsDialogVisible = true
    },
    // 提交角色表单
    submitForm() {
      this.$refs.roleFormRef.validate(valid => {
        if (!valid) return
        
        if (this.editMode) {
          // 编辑角色逻辑
          this.$message.success('更新角色成功')
        } else {
          // 添加角色逻辑
          this.$message.success('添加角色成功')
        }
        this.dialogVisible = false
        this.getRoleList()
      })
    },
    // 提交权限表单
    submitRightsForm() {
      const checkedKeys = this.$refs.menuTree.getCheckedKeys()
      const halfCheckedKeys = this.$refs.menuTree.getHalfCheckedKeys()
      const allKeys = [...checkedKeys, ...halfCheckedKeys]
      
      // 模拟API调用
      console.log('分配权限:', this.currentRoleId, allKeys)
      this.$message.success('分配权限成功')
      this.rightsDialogVisible = false
      this.getRoleList()
    },
    // 重置角色表单
    resetForm() {
      this.$refs.roleFormRef?.resetFields()
      this.roleForm = {
        id: null,
        name: '',
        description: ''
      }
    },
    // 重置权限表单
    resetRightsForm() {
      this.currentRoleId = null
      this.defaultCheckedKeys = []
    }
  }
}
</script>

<style scoped>
.role-manage-container {
  padding: 20px;
}

.menu-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>