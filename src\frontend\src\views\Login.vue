<template>
  <div class="login-container">
    <!-- Logo区域 -->
    <div class="logo-container">
      <h1 class="logo">轻量级管理系统</h1>
    </div>
    
    <!-- 登录卡片 -->
    <div class="login-card glass-card">
      <h2>系统登录</h2>
      <el-form 
        :model="loginForm" 
        :rules="loginRules" 
        ref="loginFormRef" 
        label-width="0px">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" prefix-icon="el-icon-user" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" prefix-icon="el-icon-lock" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="login" class="login-button">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 底部版权信息 -->
    <div class="footer">
      <p>© 2023 轻量级管理系统 版权所有</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      // 登录表单数据
      loginForm: {
        username: '',
        password: ''
      },
      // 表单验证规则
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 50, message: '用户名长度在3到50个字符之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 40, message: '密码长度在6到40个字符之间', trigger: 'blur' }
        ]
      },
      // 加载状态
      loading: false
    }
  },
  methods: {
    // 登录方法
    login() {
      this.$refs.loginFormRef.validate(async valid => {
        if (!valid) return
        
        this.loading = true
        try {
          await this.$store.dispatch('login', this.loginForm)
          await this.$store.dispatch('getMenus')
          this.$message.success('登录成功')
          this.$router.push('/home')
        } catch (error) {
          this.$message.error(error.response?.data?.message || '登录失败，请检查用户名和密码')
        } finally {
          this.loading = false
        }
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #e6f7ff 0%, #d0e7ff 100%);
  padding: 20px;
}

.logo-container {
  margin-top: 40px;
  text-align: center;
}

.logo {
  color: var(--secondary-color);
  font-size: 28px;
  margin: 0;
}

.login-card {
  width: 400px;
  padding: 30px;
  text-align: center;
}

.login-card h2 {
  color: var(--secondary-color);
  margin-bottom: 30px;
}

.login-button {
  width: 100%;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.footer {
  margin-top: 40px;
  color: var(--light-text-color);
  font-size: 14px;
}

/* 响应式设计 */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  .login-card {
    width: 360px;
  }
}

@media screen and (max-width: 767px) {
  .login-card {
    width: 90%;
  }
  
  .logo {
    font-size: 24px;
  }
}
</style>