import axios from 'axios'

/**
 * 菜单相关的API接口封装
 */
export default {
  /**
   * 获取当前用户的菜单
   * @returns {Promise} - 返回Promise对象
   */
  getUserMenus() {
    return axios.get('/menus/user')
  },

  /**
   * 获取所有菜单
   * @returns {Promise} - 返回Promise对象
   */
  getAllMenus() {
    return axios.get('/menus')
  },

  /**
   * 根据ID获取菜单
   * @param {Number} id - 菜单ID
   * @returns {Promise} - 返回Promise对象
   */
  getMenuById(id) {
    return axios.get(`/menus/${id}`)
  },

  /**
   * 创建菜单
   * @param {Object} data - 菜单信息
   * @returns {Promise} - 返回Promise对象
   */
  createMenu(data) {
    return axios.post('/menus', data)
  },

  /**
   * 更新菜单
   * @param {Number} id - 菜单ID
   * @param {Object} data - 菜单信息
   * @returns {Promise} - 返回Promise对象
   */
  updateMenu(id, data) {
    return axios.put(`/menus/${id}`, data)
  },

  /**
   * 删除菜单
   * @param {Number} id - 菜单ID
   * @returns {Promise} - 返回Promise对象
   */
  deleteMenu(id) {
    return axios.delete(`/menus/${id}`)
  }
}