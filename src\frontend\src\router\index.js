import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

// 路由懒加载
const Login = () => import('../views/Login.vue')
const Home = () => import('../views/Home.vue')
const Welcome = () => import('../views/Welcome.vue')
const UserManage = () => import('../views/system/UserManage.vue')
const RoleManage = () => import('../views/system/RoleManage.vue')
const MenuManage = () => import('../views/system/MenuManage.vue')

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    redirect: '/welcome',
    children: [
      {
        path: '/welcome',
        name: 'Welcome',
        component: Welcome
      },
      {
        path: '/system/user',
        name: 'UserManage',
        component: UserManage
      },
      {
        path: '/system/role',
        name: 'RoleManage',
        component: RoleManage
      },
      {
        path: '/system/menu',
        name: 'MenuManage',
        component: MenuManage
      }
    ]
  }
]

const router = new VueRouter({
  routes
})

// 导航守卫
router.beforeEach((to, from, next) => {
  // 如果访问的是登录页，直接放行
  if (to.path === '/login') return next()
  
  // 获取token
  const token = localStorage.getItem('token')
  
  // 如果没有token，强制跳转到登录页
  if (!token) return next('/login')
  
  // 有token，放行
  next()
})

export default router