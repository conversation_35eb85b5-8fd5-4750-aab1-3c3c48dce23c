import axios from 'axios'

/**
 * 认证相关的API接口封装
 */
export default {
  /**
   * 用户登录
   * @param {Object} data - 登录信息
   * @returns {Promise} - 返回Promise对象
   */
  login(data) {
    return axios.post('/auth/login', data)
  },

  /**
   * 用户注册
   * @param {Object} data - 注册信息
   * @returns {Promise} - 返回Promise对象
   */
  register(data) {
    return axios.post('/auth/register', data)
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} - 返回Promise对象
   */
  getUserInfo() {
    return axios.get('/users/me')
  }
}