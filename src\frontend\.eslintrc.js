module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 添加自定义规则
    'semi': ['warn', 'never'], // 不使用分号
    'quotes': ['warn', 'single'], // 使用单引号
    'indent': ['warn', 2], // 缩进2个空格
    'vue/html-indent': ['warn', 2], // Vue模板缩进2个空格
    'vue/max-attributes-per-line': ['warn', {
      'singleline': 3,
      'multiline': {
        'max': 1,
        'allowFirstLine': false
      }
    }]
  }
}