-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE
);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- 创建菜单表
CREATE TABLE IF NOT EXISTS menus (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    path VARCHAR(100),
    component VARCHAR(100),
    icon VARCHAR(50),
    parent_id BIGINT,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (parent_id) REFERENCES menus(id)
);

-- 创建角色菜单关联表
CREATE TABLE IF NOT EXISTS role_menus (
    role_id BIGINT NOT NULL,
    menu_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, menu_id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (menu_id) REFERENCES menus(id)
);

-- 插入初始角色
INSERT INTO roles (name) VALUES ('ROLE_ADMIN');
INSERT INTO roles (name) VALUES ('ROLE_USER');

-- 插入初始管理员用户 (密码: Admin@9527#)
INSERT INTO users (username, password, name, email) 
VALUES ('admin', '$2a$10$EqWWNlQOEYKLJCQM/7CaUOiUVs1r0Hd.tWS.sSLWnIgQBncUu5/6.', '管理员', '<EMAIL>');

-- 分配管理员角色
INSERT INTO user_roles (user_id, role_id) 
VALUES (1, 1);

-- 插入初始菜单
INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order) 
VALUES (1, '首页', '/dashboard', 'Dashboard', 'el-icon-s-home', NULL, 1);

INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order) 
VALUES (2, '系统管理', '/system', 'Layout', 'el-icon-setting', NULL, 2);

INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order) 
VALUES (3, '用户管理', 'user', 'system/user/index', 'el-icon-user', 2, 1);

INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order) 
VALUES (4, '角色管理', 'role', 'system/role/index', 'el-icon-s-check', 2, 2);

INSERT INTO menus (id, name, path, component, icon, parent_id, sort_order) 
VALUES (5, '菜单管理', 'menu', 'system/menu/index', 'el-icon-menu', 2, 3);

-- 分配菜单权限
INSERT INTO role_menus (role_id, menu_id) VALUES (1, 1);
INSERT INTO role_menus (role_id, menu_id) VALUES (1, 2);
INSERT INTO role_menus (role_id, menu_id) VALUES (1, 3);
INSERT INTO role_menus (role_id, menu_id) VALUES (1, 4);
INSERT INTO role_menus (role_id, menu_id) VALUES (1, 5);
INSERT INTO role_menus (role_id, menu_id) VALUES (2, 1);