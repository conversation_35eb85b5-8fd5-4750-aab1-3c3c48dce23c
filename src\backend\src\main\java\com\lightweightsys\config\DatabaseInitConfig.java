package com.lightweightsys.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.init.DataSourceInitializer;
import org.springframework.jdbc.datasource.init.DatabasePopulator;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

import javax.sql.DataSource;

/**
 * 数据库初始化配置
 * 在应用启动时自动执行SQL脚本初始化数据库
 */
@Configuration
public class DatabaseInitConfig {

    /**
     * 是否启用数据库初始化
     */
    @Value("${app.db-init.enabled:true}")
    private boolean enabled;

    /**
     * 初始化脚本路径
     */
    @Value("${app.db-init.script-location:sql/exam.sql}")
    private String scriptLocation;

    /**
     * 配置数据源初始化器
     *
     * @param dataSource 数据源
     * @return 数据源初始化器
     */
    @Bean
    public DataSourceInitializer dataSourceInitializer(final DataSource dataSource) {
        final DataSourceInitializer initializer = new DataSourceInitializer();
        initializer.setDataSource(dataSource);
        initializer.setDatabasePopulator(databasePopulator());
        initializer.setEnabled(enabled);
        return initializer;
    }

    /**
     * 配置数据库填充器
     *
     * @return 数据库填充器
     */
    private DatabasePopulator databasePopulator() {
        final ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
        populator.addScript(loadScript());
        populator.setSeparator(";");
        populator.setContinueOnError(false);
        return populator;
    }

    /**
     * 加载SQL脚本资源
     *
     * @return SQL脚本资源
     */
    private Resource loadScript() {
        return new ClassPathResource(scriptLocation);
    }
}