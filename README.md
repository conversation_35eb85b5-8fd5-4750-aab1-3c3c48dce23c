# 轻量级管理系统 (lightweightsys)

这是一个基于Java+Vue构建的前后端分离的轻量级管理系统，采用Spring Boot作为后端框架，Vue.js作为前端框架。

## 系统架构

### 后端技术栈
- Spring Boot 2.7.x
- Spring Security
- Spring Data JPA
- H2 数据库
- JWT 认证
- Maven 构建工具

### 前端技术栈
- Vue 2.6.x
- Vue Router
- Vuex
- Element UI
- Axios

## 目录结构

```
lightweightsys/
├── sql/                    # 数据库脚本
│   └── exam.sql            # 初始化数据库脚本
├── src/
│   ├── backend/            # 后端项目
│   │   ├── src/main/java/  # Java源代码
│   │   ├── src/main/resources/ # 资源文件
│   │   ├── src/test/       # 测试代码
│   │   ├── pom.xml         # Maven配置文件
│   │   └── mvnw            # Maven包装器
│   └── frontend/           # 前端项目
│       ├── src/            # Vue源代码
│       ├── public/         # 静态资源
│       └── package.json    # NPM配置文件
└── README.md               # 项目说明文档
```

## 系统启动方式

### 后端启动

1. **进入后端项目目录**

```bash
cd src/backend
```

2. **使用Maven编译项目**

Windows环境:
```bash
mvnw.cmd clean install
```

Unix/Linux环境:
```bash
./mvnw clean install
```

3. **运行Spring Boot应用**

Windows环境:
```bash
mvnw.cmd spring-boot:run
```

Unix/Linux环境:
```bash
./mvnw spring-boot:run
```

后端服务将在 `http://localhost:8080/api` 上运行。

### 前端启动

1. **进入前端项目目录**

```bash
cd src/frontend
```

2. **安装依赖**

```bash
npm install
```

3. **启动开发服务器**

```bash
npm run serve
```

前端应用将在 `http://localhost:8081` 上运行。

## 系统访问

1. 打开浏览器，访问 `http://localhost:8081`
2. 使用以下默认账号登录系统：
   - 用户名：admin
   - 密码：Admin@9527#

## 数据库访问

系统使用H2内存数据库，可以通过以下方式访问H2控制台：

1. 启动后端服务
2. 打开浏览器，访问 `http://localhost:8080/api/h2-console`
3. 使用以下配置连接数据库：
   - JDBC URL: `jdbc:h2:mem:lightweightsysdb`
   - 用户名: `sa`
   - 密码: `H2db@7294#5m`

## 系统功能

- 用户认证与授权
- 用户管理
- 角色管理
- 菜单管理
- 动态菜单加载
- 响应式界面设计

## 注意事项

1. 本系统为开发环境配置，使用H2内存数据库，系统重启后数据将丢失
2. 前后端分离部署时，需要注意跨域问题
3. 生产环境部署时，建议使用Nginx等Web服务器进行反向代理

## 开发环境

- JDK 1.8+
- Node.js 12+
- Maven 3.6+