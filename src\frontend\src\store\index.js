import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
    menus: []
  },
  mutations: {
    // 设置token
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    // 设置菜单
    SET_MENUS(state, menus) {
      state.menus = menus
    },
    // 清除用户信息
    CLEAR_USER_INFO(state) {
      state.token = ''
      state.userInfo = {}
      state.menus = []
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  },
  actions: {
    // 登录
    login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        Vue.prototype.$http.post('/auth/login', userInfo)
          .then(res => {
            const { token, id, username, name, email, roles } = res.data
            commit('SET_TOKEN', token)
            commit('SET_USER_INFO', { id, username, name, email, roles })
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    // 获取用户菜单
    getMenus({ commit }) {
      return new Promise((resolve, reject) => {
        Vue.prototype.$http.get('/menus/user')
          .then(res => {
            const { data } = res.data
            commit('SET_MENUS', data)
            resolve(data)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    // 登出
    logout({ commit }) {
      commit('CLEAR_USER_INFO')
    }
  },
  getters: {
    token: state => state.token,
    userInfo: state => state.userInfo,
    menus: state => state.menus,
    hasRole: state => role => {
      if (!state.userInfo.roles) return false
      return state.userInfo.roles.includes(role)
    }
  }
})