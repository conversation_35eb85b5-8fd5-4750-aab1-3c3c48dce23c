@echo off
echo Starting backend server...
cd src\backend
echo Current directory: %CD%
echo.
echo Checking if target directory exists...
if exist target (
    echo Target directory found
    if exist target\lightweightsys-0.0.1-SNAPSHOT.jar (
        echo JAR file found, starting application...
        java -jar target\lightweightsys-0.0.1-SNAPSHOT.jar
    ) else (
        echo JAR file not found, building project...
        call mvnw.cmd clean package -DskipTests
        if exist target\lightweightsys-0.0.1-SNAPSHOT.jar (
            echo Build successful, starting application...
            java -jar target\lightweightsys-0.0.1-SNAPSHOT.jar
        ) else (
            echo Build failed or JAR not created
            pause
        )
    )
) else (
    echo Target directory not found, building project...
    call mvnw.cmd clean package -DskipTests
    if exist target\lightweightsys-0.0.1-SNAPSHOT.jar (
        echo Build successful, starting application...
        java -jar target\lightweightsys-0.0.1-SNAPSHOT.jar
    ) else (
        echo Build failed or JAR not created
        pause
    )
)
pause
