// 主题颜色
$primary-color: #4299e1;
$secondary-color: #1a365d;
$background-color: #f0f5ff;
$text-color: #2d3748;
$light-text-color: #718096;
$border-color: #e2e8f0;

// 玻璃态效果
$glass-background: rgba(255, 255, 255, 0.7);
$glass-border: 1px solid rgba(255, 255, 255, 0.2);
$glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
$glass-blur: blur(10px);

// 响应式断点
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 15px;

// 阴影
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);