# Vue.js 忽略规则
.DS_Store
node_modules
/dist

# 本地环境文件
.env.local
.env.*.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.vscode
.codebuddy

# Java 忽略规则
# 编译后的类文件
*.class

# 日志文件
*.log

# BlueJ 文件
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# 打包文件
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# 虚拟机崩溃日志
hs_err_pid*
replay_pid*

# Maven 忽略规则
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle 忽略规则
.gradle
**/build/
!src/**/build/
gradle-app.setting
!gradle-wrapper.jar
.gradletasknamecache

# Spring Boot 忽略规则
*.properties
*.yml
!src/main/resources/application.properties
!src/main/resources/application.yml