package com.lightweightsys.controller;

import com.lightweightsys.dto.ApiResponse;
import com.lightweightsys.dto.MenuDto;
import com.lightweightsys.service.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单控制器
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/menus")
public class MenuController {
    
    @Autowired
    private MenuService menuService;

    /**
     * 获取当前用户的菜单
     *
     * @return 菜单列表
     */
    @GetMapping("/user")
    public ResponseEntity<?> getUserMenus() {
        List<MenuDto> menus = menuService.getMenusForCurrentUser();
        return ResponseEntity.ok(ApiResponse.success("获取菜单成功", menus));
    }

    /**
     * 获取所有菜单
     *
     * @return 菜单列表
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllMenus() {
        List<MenuDto> menus = menuService.getAllMenus();
        return ResponseEntity.ok(ApiResponse.success("获取所有菜单成功", menus));
    }

    /**
     * 根据ID获取菜单
     *
     * @param id 菜单ID
     * @return 菜单
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getMenuById(@PathVariable Long id) {
        MenuDto menu = menuService.getMenuById(id);
        return ResponseEntity.ok(ApiResponse.success("获取菜单成功", menu));
    }

    /**
     * 创建菜单
     *
     * @param menuDto 菜单DTO
     * @return 创建结果
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createMenu(@RequestBody MenuDto menuDto) {
        MenuDto createdMenu = menuService.createMenu(menuDto);
        return ResponseEntity.ok(ApiResponse.success("创建菜单成功", createdMenu));
    }

    /**
     * 更新菜单
     *
     * @param id 菜单ID
     * @param menuDto 菜单DTO
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateMenu(@PathVariable Long id, @RequestBody MenuDto menuDto) {
        MenuDto updatedMenu = menuService.updateMenu(id, menuDto);
        return ResponseEntity.ok(ApiResponse.success("更新菜单成功", updatedMenu));
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteMenu(@PathVariable Long id) {
        menuService.deleteMenu(id);
        return ResponseEntity.ok(ApiResponse.success("删除菜单成功"));
    }
}