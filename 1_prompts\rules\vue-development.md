# Vue开发规范

## 技术栈约定
- **前端框架**: Vue 2.6.x + Options API
- **UI组件库**: Element UI
- **开发语言**: 原生JavaScript (不使用TypeScript)
- **状态管理**: Vuex
- **HTTP客户端**: Axios
- **样式**: CSS3 + CSS Variables

## 项目结构规范

### 目录组织
- **src/assets**: 静态资源文件（图片、字体、全局CSS等）
- **src/components**: 可复用组件
- **src/views**: 页面组件
- **src/router**: 路由配置
- **src/store**: 状态管理
- **src/api**: API接口封装
- **public**: 静态资源文件（不经过webpack处理）

### 命名规范
- **文件命名**: 使用kebab-case（如`login-form.vue`）
- **组件命名**: 使用PascalCase（如`LoginForm`）
- **变量命名**: 使用camelCase（如`loginForm`）
- **常量命名**: 使用UPPER_SNAKE_CASE（如`API_URL`）

## 组件开发规范

### 组件结构
- 每个组件应遵循`<template>`, `<script>`, `<style>`顺序
- 组件应尽可能小，专注于单一功能
- 复杂组件应拆分为多个小组件

### 组件通信
- 父子组件通信：使用props和$emit
- 跨组件通信：使用Vuex
- 避免使用事件总线（EventBus）

### 样式规范
- 优先使用scoped样式
- 使用CSS变量定义主题色和常用样式
- 响应式设计使用媒体查询
- 避免使用!important

## API接口规范

### API封装
- API接口应集中管理在`src/api`目录下
- 按功能模块划分API文件（如`auth.js`）
- 使用Axios拦截器统一处理请求和响应

### 请求格式
- GET请求：用于获取数据
- POST请求：用于提交数据
- 请求头统一设置：Content-Type和Authorization

### 响应处理
- 统一处理响应状态码
- 使用Promise处理异步请求
- 错误处理应统一在拦截器中进行

## 路由规范

### 路由配置
- 路由配置集中在`src/router/index.js`
- 使用路由懒加载提高性能
- 路由命名应与组件名称保持一致

### 路由守卫
- 使用全局前置守卫处理权限验证
- 登录状态验证应在路由守卫中进行
- 未登录用户重定向到登录页面

## 状态管理规范

### Vuex使用
- 按功能模块划分store
- 状态更新必须通过mutations
- 异步操作必须在actions中处理
- 避免在组件中直接修改state

### 状态持久化
- 用户登录状态存储在localStorage
- 敏感信息不应明文存储
- 页面刷新后恢复状态

## 表单处理规范

### 表单验证
- 使用Element UI的表单验证功能
- 定义清晰的验证规则
- 提供友好的错误提示

### 表单提交
- 提交前进行表单验证
- 显示加载状态
- 处理提交成功和失败情况

## 安全规范

### 认证授权
- 使用JWT进行身份验证
- 在请求头中添加Authorization
- 处理token过期情况

### 数据安全
- 敏感数据不应明文存储
- 避免在URL中传递敏感信息
- 防止XSS和CSRF攻击

## UI设计规范

### 设计风格
- 遵循Glassmorphism Tech Blue UI设计风格
- 使用半透明玻璃态效果
- 主色调为深蓝色(#1a365d)和浅蓝色(#4299e1)或者蓝白配色

### 响应式设计
- 桌面端（≥1024px）：登录卡片宽度为400px
- 平板端（768px-1023px）：登录卡片宽度为360px
- 移动端（<768px）：登录卡片宽度为90%

### 交互设计
- 提供明确的视觉反馈
- 表单提交显示加载状态
- 错误信息清晰可见

## 性能优化规范

### 代码优化
- 使用路由懒加载
- 避免不必要的组件渲染
- 合理使用计算属性和监听器

### 资源优化
- 压缩静态资源
- 使用CDN加载第三方库
- 合理使用缓存

## 开发流程规范

### 开发环境
- 使用Vue CLI创建项目
- 开发服务器代理API请求
- 使用ESLint保持代码风格一致、创建Vue项目的配置文件，以确保ESLint能够正确工作,确保没有ESLint错误和警告
- vue.config.js文件引用保证存在

### 测试与部署
- 在提交前进行本地测试
- 构建生产环境代码
- 部署到生产服务器

## 最佳实践

### 代码质量
- 遵循DRY原则，避免重复代码
- 组件设计遵循单一职责原则
- 代码注释清晰明了

### 用户体验
- 提供友好的加载状态
- 表单验证即时反馈
- 错误处理友好提示

### 可维护性
- 代码结构清晰
- 命名规范一致
- 文档完善