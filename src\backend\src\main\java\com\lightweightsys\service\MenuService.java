package com.lightweightsys.service;

import com.lightweightsys.dto.MenuDto;
import com.lightweightsys.model.Menu;
import com.lightweightsys.model.Role;
import com.lightweightsys.model.User;
import com.lightweightsys.repository.MenuRepository;
import com.lightweightsys.repository.UserRepository;
import com.lightweightsys.security.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单服务类
 */
@Service
public class MenuService {
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 获取当前用户的菜单
     *
     * @return 菜单DTO列表
     */
    @Transactional(readOnly = true)
    public List<MenuDto> getMenusForCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        
        User user = userRepository.findById(userDetails.getId())
                .orElseThrow(() -> new EntityNotFoundException("用户不存在"));
        
        Set<Menu> userMenus = new HashSet<>();
        for (Role role : user.getRoles()) {
            userMenus.addAll(role.getMenus());
        }
        
        // 只获取顶级菜单
        List<Menu> topLevelMenus = userMenus.stream()
                .filter(menu -> menu.getParent() == null)
                .sorted((m1, m2) -> m1.getSortOrder() - m2.getSortOrder())
                .collect(Collectors.toList());
        
        return convertToMenuDtoList(topLevelMenus);
    }
    
    /**
     * 获取所有菜单
     *
     * @return 菜单DTO列表
     */
    @Transactional(readOnly = true)
    public List<MenuDto> getAllMenus() {
        List<Menu> topLevelMenus = menuRepository.findByParentIsNullOrderBySortOrder();
        return convertToMenuDtoList(topLevelMenus);
    }
    
    /**
     * 根据ID获取菜单
     *
     * @param id 菜单ID
     * @return 菜单DTO
     */
    @Transactional(readOnly = true)
    public MenuDto getMenuById(Long id) {
        Menu menu = menuRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("菜单不存在"));
        return convertToMenuDto(menu);
    }
    
    /**
     * 创建菜单
     *
     * @param menuDto 菜单DTO
     * @return 创建的菜单DTO
     */
    @Transactional
    public MenuDto createMenu(MenuDto menuDto) {
        Menu menu = new Menu();
        updateMenuFromDto(menu, menuDto);
        Menu savedMenu = menuRepository.save(menu);
        return convertToMenuDto(savedMenu);
    }
    
    /**
     * 更新菜单
     *
     * @param id 菜单ID
     * @param menuDto 菜单DTO
     * @return 更新后的菜单DTO
     */
    @Transactional
    public MenuDto updateMenu(Long id, MenuDto menuDto) {
        Menu menu = menuRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("菜单不存在"));
        updateMenuFromDto(menu, menuDto);
        Menu updatedMenu = menuRepository.save(menu);
        return convertToMenuDto(updatedMenu);
    }
    
    /**
     * 删除菜单
     *
     * @param id 菜单ID
     */
    @Transactional
    public void deleteMenu(Long id) {
        Menu menu = menuRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("菜单不存在"));
        menuRepository.delete(menu);
    }
    
    /**
     * 将菜单实体转换为菜单DTO
     *
     * @param menu 菜单实体
     * @return 菜单DTO
     */
    private MenuDto convertToMenuDto(Menu menu) {
        if (menu == null) {
            return null;
        }
        
        MenuDto menuDto = new MenuDto();
        menuDto.setId(menu.getId());
        menuDto.setName(menu.getName());
        menuDto.setPath(menu.getPath());
        menuDto.setComponent(menu.getComponent());
        menuDto.setIcon(menu.getIcon());
        menuDto.setSortOrder(menu.getSortOrder());
        
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            List<MenuDto> childrenDtos = menu.getChildren().stream()
                    .sorted((m1, m2) -> m1.getSortOrder() - m2.getSortOrder())
                    .map(this::convertToMenuDto)
                    .collect(Collectors.toList());
            menuDto.setChildren(childrenDtos);
        }
        
        return menuDto;
    }
    
    /**
     * 将菜单实体列表转换为菜单DTO列表
     *
     * @param menus 菜单实体列表
     * @return 菜单DTO列表
     */
    private List<MenuDto> convertToMenuDtoList(List<Menu> menus) {
        if (menus == null) {
            return new ArrayList<>();
        }
        
        return menus.stream()
                .map(this::convertToMenuDto)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据DTO更新菜单实体
     *
     * @param menu 菜单实体
     * @param menuDto 菜单DTO
     */
    private void updateMenuFromDto(Menu menu, MenuDto menuDto) {
        menu.setName(menuDto.getName());
        menu.setPath(menuDto.getPath());
        menu.setComponent(menuDto.getComponent());
        menu.setIcon(menuDto.getIcon());
        menu.setSortOrder(menuDto.getSortOrder());
        
        if (menuDto.getId() != null && !menuDto.getId().equals(menu.getId())) {
            Menu parent = menuRepository.findById(menuDto.getId())
                    .orElseThrow(() -> new EntityNotFoundException("父菜单不存在"));
            menu.setParent(parent);
        }
    }
}