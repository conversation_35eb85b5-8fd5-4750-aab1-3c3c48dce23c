# Java开发规范

## 技术栈选择
- **核心框架**: Spring Boot 2.7.x
- **安全框架**: Spring Security
- **数据访问**: Spring Data JPA
- **数据库**: H2 (开发环境)
- **API认证**: JWT (JSON Web Token)
- **工具库**: Lombok, Validation API
- **构建工具**: Maven 3.5.3

## 核心原则
- 严格遵循 **SOLID、DRY、KISS、YAGNI** 原则
- 遵循 **OWASP 安全最佳实践**（输入验证、SQL注入防护）
- 采用 **分层架构设计**，确保职责分离
- 代码变更需通过 **单元测试覆盖**（测试覆盖率 ≥ 80%）
- 每次写完所有的代码都要进行build操作确认是否有语法错误

## 项目架构规范

### 模块划分
- **model**: 实体类定义，包含JPA实体和枚举类型
- **repository**: 数据访问层，定义JPA仓库接口
- **service**: 业务逻辑层，实现核心业务功能
- **controller**: API控制器，处理HTTP请求
- **config**: 配置类，包含安全配置等
- **security**: 安全相关类，包含JWT工具和认证过滤器
- **dto**: 数据传输对象，用于请求和响应数据封装

### 分层职责规范

#### Controller层
- **职责边界**: 仅负责参数校验和请求路由，**禁止编写业务逻辑**
- **进行接口安全校验、幂等性校验、token校验解析等操作**
- **API规范**:
  - 请求方法：使用合适的HTTP方法（GET, POST, PUT, DELETE）
  - 参数规范：超过3个参数必须定义DTO实体
  - 响应格式：统一使用ResponseEntity封装
- **路径规范**:
  - API前缀: `/api/`
  - 认证相关: `/api/auth/`

#### Service层
- **职责**: 业务逻辑实现、参数校验、权限控制、事务管理
- **设计原则**: 一个业务领域一个Service，一个功能一个方法
- **事务规范**: 跨表写操作必须使用 `@Transactional(rollbackFor = Exception.class)`
- **数据类型**: 入参和返回参使用DTO类型或实体类型，避免使用多个变量作为参数

#### Repository层
- **职责**: 数据库访问，包含通用的增删改查
- **技术选型**: 使用Spring Data JPA接口
- **接口规范**: 必须继承`JpaRepository<Entity, ID>`
- **数据类型**: 入参使用Entity或ID类型，返回使用Entity或自定义投影

## 代码规范

### 命名规范
- **类名**: 使用PascalCase，如`UserController`、`AuthService`
- **方法名**: 使用camelCase，如`findByUsername`、`createUser`
- **变量名**: 使用camelCase，如`userRepository`、`passwordEncoder`
- **常量名**: 使用UPPER_SNAKE_CASE，如`JWT_SECRET`、`JWT_EXPIRATION`

### 注释规范
- **类注释**: 描述类的功能和职责
- **方法注释**: 描述方法的功能、参数和返回值
- **复杂逻辑注释**: 对复杂业务逻辑添加详细注释

### 异常处理
- **业务异常**: 使用自定义异常类表示业务逻辑错误
- **全局异常处理**: 使用`@ControllerAdvice`进行统一异常处理
- **异常日志**: 记录详细的异常信息，包括堆栈和参数

## 安全规范

### 认证与授权
- **JWT认证**: 使用JWT进行无状态认证
- **密码加密**: 使用BCrypt加密存储密码
- **角色权限**: 基于角色的访问控制(RBAC)

### 数据安全
- **输入验证**: 使用Bean Validation进行参数校验
- **SQL注入防护**: 使用参数化查询和JPA
- **XSS防护**: 对输出内容进行适当转义

## DTO规范
- **命名规范**: 功能明确命名，如`LoginRequest`、`JwtResponse`
- **字段校验**: 使用Bean Validation注解进行参数校验（`@NotBlank`, `@Size`, `@Email`等）
- **对象转换**: 避免使用工具类进行对象拷贝，采用手动字段赋值

## 实体类规范
- **注解使用**: 合理使用JPA注解（`@Entity`, `@Table`, `@Column`等）
- **关系映射**: 明确定义实体间关系（`@OneToMany`, `@ManyToOne`等）
- **字段验证**: 使用Bean Validation注解进行字段验证

## 配置管理
- **配置文件格式**: 统一使用YAML格式（.yml）而非Properties格式
- **环境配置**: 使用Spring Profiles管理不同环境配置
- **敏感信息**: 避免在代码中硬编码敏感信息，使用配置文件或环境变量
- **配置结构**: YAML配置应保持层次清晰，使用缩进表示层级关系
- **多环境配置**: 使用`application-{profile}.yml`命名不同环境的配置文件

### YAML配置示例
```yaml
# 数据库配置
spring:
  datasource:
    url: jdbc:h2:mem:logindb
    username: sa
    password: password
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    
# 自定义配置
app:
  security:
    jwt-secret: ${JWT_SECRET:defaultSecretKey}
    jwt-expiration: 86400000
```

## 日志规范
- **日志级别**: 合理使用不同日志级别（ERROR, WARN, INFO, DEBUG）
- **敏感信息**: 避免记录敏感信息（密码、token等）
- **异常日志**: 记录完整的异常堆栈信息

## 测试规范
- **单元测试**: 使用JUnit和Mockito进行单元测试
- **测试覆盖**: 核心业务逻辑测试覆盖率不低于80%
- **集成测试**: 使用Spring Boot Test进行集成测试

## 严格禁止事项
1. **文件修改**: 禁止修改核心配置文件（未经允许）
2. **对象拷贝**: 避免使用工具类进行对象拷贝，应手动赋值
3. **循环查询**: 禁止在循环中查询数据库，应使用批量查询
4. **硬编码**: 避免硬编码配置信息和业务常量
5. **直接操作数据库**: Service层禁止直接操作数据库，必须通过Repository访问
6. **使用Properties文件**: 禁止使用.properties格式的配置文件，统一使用YAML格式

## 最佳实践总结

### 关注点分离
- Controller层专注请求处理和参数校验
- Service层专注业务逻辑实现
- Repository层专注数据访问

### 可维护性
- 使用枚举管理状态
- 使用DTO传输数据
- 手动字段赋值提高可读性
- 完善的异常处理和日志记录
- 使用YAML格式配置文件提高可读性和维护性

### 性能优化
- 合理使用缓存
- 避免N+1查询问题
- 使用批量操作替代循环操作