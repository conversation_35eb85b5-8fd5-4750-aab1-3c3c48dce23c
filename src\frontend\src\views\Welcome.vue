<template>
  <div class="welcome-container glass-card">
    <h1>欢迎使用轻量级管理系统</h1>
    <div class="welcome-content">
      <div class="welcome-info">
        <h2>用户信息</h2>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.name || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo.email || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag v-for="(role, index) in userInfo.roles" :key="index" type="primary" class="role-tag">
              {{ role }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="welcome-stats">
        <h2>系统信息</h2>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-info">
                <div class="stat-title">用户数量</div>
                <div class="stat-value">{{ stats.userCount }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-s-check"></i>
              </div>
              <div class="stat-info">
                <div class="stat-title">角色数量</div>
                <div class="stat-value">{{ stats.roleCount }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-menu"></i>
              </div>
              <div class="stat-info">
                <div class="stat-title">菜单数量</div>
                <div class="stat-value">{{ stats.menuCount }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Welcome',
  data() {
    return {
      stats: {
        userCount: 1,
        roleCount: 2,
        menuCount: 5
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  }
}
</script>

<style scoped>
.welcome-container {
  padding: 20px;
}

.welcome-container h1 {
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: 30px;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-info h2,
.welcome-stats h2 {
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.role-tag {
  margin-right: 5px;
}

.stat-card {
  background: var(--glass-background);
  border-radius: 10px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 15px;
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 40px;
  color: var(--primary-color);
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: var(--light-text-color);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--secondary-color);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
  }
  
  .el-col {
    margin-bottom: 15px;
  }
}
</style>