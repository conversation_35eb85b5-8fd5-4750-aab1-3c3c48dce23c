package com.lightweightsys.repository;

import com.lightweightsys.model.Menu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单数据访问接口
 */
@Repository
public interface MenuRepository extends JpaRepository<Menu, Long> {
    
    /**
     * 查找所有顶级菜单（没有父菜单的菜单）
     * 
     * @return 顶级菜单列表
     */
    List<Menu> findByParentIsNullOrderBySortOrder();
    
    /**
     * 根据角色ID查询菜单列表
     * 
     * @param roleId 角色ID
     * @return 菜单列表
     */
    @Query("SELECT m FROM Menu m JOIN m.roles r WHERE r.id = :roleId ORDER BY m.sortOrder")
    List<Menu> findByRoleId(Long roleId);
}